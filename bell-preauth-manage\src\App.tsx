import React, {useState, useEffect, useRef} from "react";
import { LocalizationState } from "bwtk";
import { IntlProvider } from "react-intl";
import { IStoreState } from "./store/Store";
import { connect } from "react-redux";
import {Container} from "@bell/bell-ui-library";
import { PaymentMethod } from "./views/PaymentMethod";
import {TermsAndCondition} from "./views/TermsAndCondition";
import { 
  // CurrentBalance
  SelectBills } from "./views/CheckboxCard";
import { defaultCreditCardInputValue, PaymentItem, defaultBankInputValue, SubscriberOffersWithBan,
  // SubscriberOffersWithBan 
} from "./models";
import { IAppOwnProps, IRequestStatus } from "./models/App";
import Config from "./Config";
import { CurrentSection, PaymentItemAccountType, PageTitleCurrentSection } from "./models/Enums";
import { getRedirectUrl, getInteracBankInfo, } from "./store/Actions";
import Loader from "./views/Loader/Loader";
import { getBanSpecificTransactionId } from "./utils";
import { Confirmation } from "./views/Confirmation";
import { APIFailure } from "./views/ErrorPage";
import { ToastMessage } from "./views/ToastMessage";
import { CancellationPartiallyFailed } from "./views/CancellationFailed";


interface IAppState {
  // config: Config | null; // config can be null initially before it is loaded
  error: string | null;
  paymentItem: PaymentItem[];
  currentSteps: any;
  isLoading: boolean;
  bankitems: any[];
  language: "en" | "fr";
  localization: LocalizationState;
  Config: Config;
  redirectUrlAction: Function;
  getInteracBankInfoAction: Function;
  creditCardAutopayOffers: SubscriberOffersWithBan[];
  debitCardAutopayOffers: SubscriberOffersWithBan[];
  removedSubscriberOffers: SubscriberOffersWithBan[];
  cancelPreauthStatus: IRequestStatus;
}


export interface IAppDispatchToProps {

}


export interface IAppMergedProps extends IAppOwnProps, IAppState, IAppDispatchToProps {

}

const AppComponent: React.FC<IAppMergedProps> = (props: IAppMergedProps) => {
  const enableSelectBills = props.Config?.getPaymentItem?.length >1 ? true : false;
  const [paymentHeadingStepState, setPaymentHeadingStepState] = useState(enableSelectBills ? "inactive" : "active");
  const [currentSteps, setCurrentSteps] = useState(":");
  const [currentSection, setCurrentSection] = useState(enableSelectBills ? CurrentSection.SelectBills : CurrentSection.PaymentMethod);
  const [creditCardInputValue, setCreditCardInputValue] = useState(defaultCreditCardInputValue);
  const [BankInputValue, setBankInputValue] = useState(defaultBankInputValue);
  const [isBankSelected, setIsBankSelected] = useState(false);
  const [checkedBillItems, setCheckedBillItems] = useState(enableSelectBills === false ? props.Config?.getPaymentItem : []);
  const [accountInputValues, setAccountInputValues] = useState(enableSelectBills === false
    ? [{
      accountNumber: props.Config?.getPaymentItem[0]?.Ban,
      subNumber: props.Config?.getPaymentItem[0]?.subscriberId, // This can be null or undefined if not provided
      transactionID: getBanSpecificTransactionId(props.Config?.getPaymentItem[0]?.Ban, props.Config?.transactionIdArray),
      payBalanceAmnt: 0,
    }]
    : [],
  );
  // const [checkedCurrentBalanceItems, setCheckedCurrentBalanceItems] = useState([]);
  const [isBanCreditPreauth ] = useState(creditCardInputValue && creditCardInputValue.cardNumber ? true : false);
  const [apiSatusIsFailed, setApiSatusIsFailed] = useState(false);
  const [cancelPreauthSectionClicked, setCancelPreauthSectionClicked] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [InteracCode, setInteracCode] = useState("");
  const [someCancellationFailed, setSomeCancellationFailed] = useState(false);
  const [bansCancellationFailed, setBansCancellationFailed] = useState<PaymentItem[]>([]);

  const {localization, Config, isLoading} = props;

  const onCurrentSteps = (step: any) => {
    if (currentSteps !== step) {
      setCurrentSteps(step);
    }
  };

  const getQueryParameter = (param: string) => new URLSearchParams(document.location.search.substring(1)).get(param);

  const removeSessionStorageCheckedItems = () => {
    const items = sessionStorage.getItem("itemsChecked");
    if (items && items.length > 0) {
      sessionStorage.removeItem("itemsChecked");
    }
  };
 
  useEffect(() => {
    const code = getQueryParameter("code");
    if (code && code != null) {
      setInteracCode(code);
      const newUrl = Config?.currentUrl?.substring(0, Config?.currentUrl?.lastIndexOf("/")+1)+"GetFieldModifyViewManage";
      window.history.pushState({ path: newUrl }, '', newUrl);
      props.getInteracBankInfoAction(code);
      if (Config?.getPaymentItem?.length === 1) {
        removeSessionStorageCheckedItems();
      }
    }
    else {
      removeSessionStorageCheckedItems();
      // props.redirectUrlAction({});
    }
  }, []);

  useEffect(() => {
    props.redirectUrlAction({});   // call redirecturl code on every load
  }, []);
  
  useEffect(() => {
    if(apiSatusIsFailed) {
      const container = document.getElementById("container");
      if(container && container.childElementCount > 0){
        container.replaceWith(container.cloneNode(true));

      }
    }
  }, [apiSatusIsFailed]);

  const prevStepsRef = useRef(currentSteps);

  useEffect(() => {
    prevStepsRef.current = currentSteps;
  });

  useEffect(() => {
    let pageTitle = Config.pagetitle || "";
    const PageTitleConfirmation = pageTitle.replace(/Step/g, '');
    if (prevStepsRef.current !== currentSteps) {
      pageTitle = `${pageTitle} ${currentSteps}`;
      document.title = parseDOMString(pageTitle) || "";
    }

    if (currentSection === CurrentSection.Confirmation) {
      const pageConfimationTitle = `${PageTitleCurrentSection.Confirmation} ${PageTitleConfirmation}`;
      document.title = parseDOMString(pageConfimationTitle) || "";
    }

  }, [currentSteps, currentSection]);

  let enableSingleClickForPAD = Config?.getPaymentItem?.some(item => item.Due > 0 && item.isOneTimePaymentEligible && item.AccountType !== PaymentItemAccountType.OneBill && !item.isLastBillOnPreauth) ?? false;
  let enableSingleClickForPACC = Config?.getPaymentItem?.some(item => item.Due > 0 && item.isOneTimeCreditCardPaymentEnabled && item.AccountType !== PaymentItemAccountType.OneBill && !item.isLastBillOnPreauth) ?? false;
  const checkedBillItemsHasBalance = checkedBillItems?.some((x: PaymentItem) => x.Due > 0 && x.AccountType !== PaymentItemAccountType.OneBill && !x.isLastBillOnPreauth) ?? false;
  const IsAutopayCreditEnabled = Config?.IsAutopayCreditEnabled === "ON" ? true : false;
  const IsInteracEnabled = Config?.IsInteracEnabled === "ON" ? true : false;
  enableSingleClickForPAD = enableSingleClickForPAD && checkedBillItemsHasBalance;
  enableSingleClickForPACC = enableSingleClickForPACC && checkedBillItemsHasBalance;

  const parseDOMString = (e: string) =>  // Convert Spacial Characters specially for French word.
    new DOMParser().parseFromString(
      e,
      "text/html"
    ).documentElement.textContent;
  

  return (
    <IntlProvider
      locale={localization.locale}
      messages={localization.messages}
    >
      {isLoading ? <Loader /> :
        <Container>
          {!apiSatusIsFailed && !someCancellationFailed &&
          <>
            {currentSection !== CurrentSection.Confirmation &&
            <>
            
              <SelectBills
                paymentItem={Config.getPaymentItem}
                isShow={enableSelectBills}
                onCurrentSteps={onCurrentSteps}
                setCurrentSection={setCurrentSection}
                currentSection={currentSection}
                setCheckedBillItems={setCheckedBillItems}
                paymentItems={Config.getPaymentItem}
                setAccountValues={setAccountInputValues}
                accountInputValues={accountInputValues}
                transactionIds={Config.transactionIdArray}
                managePreauth={Config.selectedUpdatePaymentMethod}
                creditCardAutopayOffers = {Config.creditCardAutopayOffers}
                debitCardAutopayOffers={Config.debitCardAutopayOffers}
               
              />

              <PaymentMethod paymentItem={Config.getPaymentItem}
                isHeadingStepActive={currentSection === CurrentSection.PaymentMethod ? "active" : "inactive"}
                isSingleClickEnableForPACC={enableSingleClickForPAD}
                isSingleClickEnableForPAD={enableSingleClickForPACC}
                onCurrentSteps={onCurrentSteps}
                setHeadingSteps={setPaymentHeadingStepState}
                paymentHeadingStepState={paymentHeadingStepState}
                setInputValue={setCreditCardInputValue}
                inputValue={creditCardInputValue}
                setInputBankValue={setBankInputValue}
                inputBankValue={BankInputValue}
                setIsBankSelected={setIsBankSelected}
                setCurrentSection={setCurrentSection}
                currentSection={currentSection}
                checkedBillItems={checkedBillItems}
                bankList={Config.getBankList}
                accountInputValues={accountInputValues}
                province={Config.province}
                language={Config.language as "en" | "fr"}
                isBankSelected={isBankSelected}
                creditCardAutopayOffers = {Config.creditCardAutopayOffers}
                debitCardAutopayOffers={Config.debitCardAutopayOffers}
                removedSubscriberOffers = {Config.removedSubscriberOffers}
                managePreauth={Config.selectedUpdatePaymentMethod}
                setCancelPreauthSectionClicked={setCancelPreauthSectionClicked}
                cancelPreauthSectionClicked={cancelPreauthSectionClicked}
                setIsModalOpen={setIsModalOpen}
                setApiSatusIsFailed={setApiSatusIsFailed}
                IsAutopayCreditEnabled={IsAutopayCreditEnabled}
                IsInteracEnabled={IsInteracEnabled}
                InteracCode={InteracCode}
                setSomeCancellationFailed={setSomeCancellationFailed}
                setBansCancellationFailed={setBansCancellationFailed}
              />
              {!cancelPreauthSectionClicked &&
                <TermsAndCondition
                  isActive={currentSection === CurrentSection.TermsAndCondition}
                  onCurrentSteps={onCurrentSteps}
                  setCurrentSection={setCurrentSection}
                  currentSection={currentSection}
                  checkedBillItems={checkedBillItems}
                  paymentItem={Config.getPaymentItem}
                  province={Config.province}
                  language={Config.language}
                  userProfileProv={Config.userProfileProvince}
                  accountInputValues={accountInputValues} 
                  isBankSelected={isBankSelected}
                  setApiSatusIsFailed={setApiSatusIsFailed}
                  inputValue={creditCardInputValue}
                  creditCardAutopayOffers = {Config.creditCardAutopayOffers}
                  debitCardAutopayOffers={Config.debitCardAutopayOffers}
                />
              }
            </>
            }
            {
              currentSection === CurrentSection.Confirmation &&
              <Confirmation
                paymentItem={Config.getPaymentItem} 
                checkedBillItems={checkedBillItems} 
                showPaymentSummary={true}
                isNewbank={false} 
                inputValue={creditCardInputValue} 
                isShow={currentSection === CurrentSection.Confirmation}
                inputBankValue = {BankInputValue}
                isBankPaymentSelected = {isBankSelected}
                BankList={Config.getBankList}
                showCurrentBalance={enableSingleClickForPAD || enableSingleClickForPACC}
                language={Config.language as "en" | "fr"}
                accountInputValues={accountInputValues}
                currentSection={currentSection}    
                isBanCreditPreauth={isBanCreditPreauth}   
                creditCardAutopayOffers = {Config.creditCardAutopayOffers}
                debitCardAutopayOffers={Config.debitCardAutopayOffers}
                setApiSatusIsFailed={setApiSatusIsFailed}
                apiSatusIsFailed={apiSatusIsFailed}            
              />
            }
          </>
          }
          {apiSatusIsFailed &&
                <APIFailure></APIFailure>
          }

          <ToastMessage isModalOpen={isModalOpen} setIsModalOpen={setIsModalOpen}/>

          {someCancellationFailed && bansCancellationFailed && bansCancellationFailed.length > 0 &&
             <CancellationPartiallyFailed
               someBansFailed={bansCancellationFailed}
               language={Config.language as "en" | "fr"}
             />
          }
          
        </Container>
         
      }
    </IntlProvider >
  );
};


const mapStateToProps = (state: IStoreState, ownProps: IAppOwnProps) => ({
  localization: state.localization,
  Config: ownProps.Config,
  isLoading: state.isLoading,
  cancelPreauthStatus: state.cancelPreauthStatus,
  cancelPreauthPayments: state.cancelPreauthPayments
});

const mapDispatchToProps = (dispatch: React.Dispatch<any>) => ({
  redirectUrlAction: () => { dispatch(getRedirectUrl({})); },
  getInteracBankInfoAction: (code: string) => { dispatch(getInteracBankInfo({ code })); }
});

export const App = connect<
  IStoreState,
  IAppOwnProps
>(
  mapStateToProps as any,
  mapDispatchToProps as any
)(AppComponent);
