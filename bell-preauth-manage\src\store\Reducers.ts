import Config from "../Config";
import { CCDetails, CreditCardDetailsAction, IBankInfoRes, IGetRedirectUrl } from "../models";
import { ValidationErrors } from "../models/Error";
import { IRequestStatus } from "../models/App";
// import {IBankInfoFailure} from "../models"
export const getConfigReducer = (state: Config, _action: ReduxActions.Action<Config>) => state;
export const setConfigReducer = (_state: Config, action: ReduxActions.Action<Config>) => action.payload;

export const onCreditCardNumberChangeReducer = (state: CCDetails, { payload: newCreditCardDetails }: ReduxActions.Action<CCDetails>) => state && newCreditCardDetails ? { ...state, CreditCardNumber: newCreditCardDetails.CreditCardNumber } : state;
export const onCardHolderNameChangeReducer = (state: CCDetails, { payload: newCreditCardDetails }: ReduxActions.Action<CCDetails>) => state && newCreditCardDetails ? { ...state, CardholderName: newCreditCardDetails.CardholderName } : state;
export const onSecurityCodeChangeReducer = (state: CCDetails, { payload: newCreditCardDetails }: ReduxActions.Action<CCDetails>) => state && newCreditCardDetails ? { ...state, SecurityCode: newCreditCardDetails.SecurityCode } : state;
export const onCreditCardExpiryDateChangeReducer = (state: CCDetails, { payload: newCreditCardDetails }: ReduxActions.Action<CCDetails>) => state && newCreditCardDetails
  ? {
    ...state,
    ExpireMonth: newCreditCardDetails.ExpireMonth,
    ExpireYear: newCreditCardDetails.ExpireYear,
  }
  : state;

export const setValidationErrorsReducer = (state: ValidationErrors, action: ReduxActions.Action<ValidationErrors>): ValidationErrors => {
  switch (action.type) {
    case CreditCardDetailsAction.SET_CREDIT_CARD_VALIDATION:
      const isErrorExisting = state.errors.map(error =>
        action.payload.errors.some(payloadError => payloadError.field === error.field) ? true : false
      ).filter(filterError => filterError === true);

      if (isErrorExisting.length > 0) {
        state.errors.map(error =>
          action.payload.errors.find(payloadError => payloadError.field === error.field)
            ? { ...error, ...action.payload.errors }
            : error
        );
        return state;
      };

      return {
        ...state,
        errors: [...state.errors, ...action.payload.errors]
      };
    case CreditCardDetailsAction.RESET_CREDIT_CARD_VALIDATION:
      return {
        ...state,
        errors: []
      };
    default:
      return state;
  }
};

export const setcreatePaymentReducer = () => (state: Record<string, unknown>, { payload: createPayment }: ReduxActions.Action<Record<string, unknown>>) => ({ ...state, ...createPayment });
export const setcreatePaymentStatusReducer = (status: IRequestStatus) => (_state: IRequestStatus, { payload: _createPaymentStatus }: ReduxActions.Action<IRequestStatus>) => (status || IRequestStatus.IDLE);

export const setValidateOrderPaymentReducer = () => (state: Record<string, unknown>, { payload: validateOrderPayment }: ReduxActions.Action<Record<string, unknown>>) => ({ ...state, ...validateOrderPayment });
export const setValidateOrderPaymentStatusReducer = (status: IRequestStatus) => (_state: IRequestStatus, { payload: _validateOrderFormStatus }: ReduxActions.Action<IRequestStatus>) => (status || IRequestStatus.IDLE);

export const setsubmitOrderPaymentReducer = () => (state: Record<string, unknown>, { payload: submitOrderPayment }: ReduxActions.Action<Record<string, unknown>>) => ({ ...state, ...submitOrderPayment });
export const setsubmitOrderPaymentStatusReducer = (status: IRequestStatus) => (_state: IRequestStatus, { payload: _submitOrderFormStatus }: ReduxActions.Action<IRequestStatus>) => (status || IRequestStatus.IDLE);
export const setPasskeyReducer = () => (state: string, { payload: setPassKey }: ReduxActions.Action<string>) => setPassKey || state;

export const setGetRedirectUrlReducer = () => (state: IGetRedirectUrl, { payload: redirectUrl }: ReduxActions.Action<IGetRedirectUrl>) => ({ ...state, ...redirectUrl });
export const setInteracBankInfoReducer = () => (state: IBankInfoRes, { payload: interacBankInfo }: ReduxActions.Action<IBankInfoRes>) => ({ ...state, ...interacBankInfo });

export const setIsLoadingReducer = (state: boolean, action: ReduxActions.Action<boolean>) => action.payload!;

export const setcreateMultiPaymentReducer = () => (state: Record<string, unknown>, { payload: createPayment }: ReduxActions.Action<Record<string, unknown>>) => ({ ...state, ...createPayment });
export const setcreateMultiPaymentStatusReducer = (status: IRequestStatus) => (_state: IRequestStatus, { payload: _createPaymentStatus }: ReduxActions.Action<IRequestStatus>) => (status || IRequestStatus.IDLE);

export const setValidateMultiOrderPaymentReducer = () => (state: Record<string, unknown>, { payload: validateOrderPayment }: ReduxActions.Action<Record<string, unknown>>) => ({ ...state, ...validateOrderPayment });
export const setValidateMultiOrderPaymentStatusReducer = (status: IRequestStatus) => (_state: IRequestStatus, { payload: _validateOrderFormStatus }: ReduxActions.Action<IRequestStatus>) => (status || IRequestStatus.IDLE);

export const setsubmitMultiOrderPaymentReducer = () => (state: Record<string, unknown>, { payload: submitMultiOrderForm }: ReduxActions.Action<Record<string, unknown>>) => ({ ...state, ...submitMultiOrderForm });
export const setsubmitMultiOrderPaymentStatusReducer = (status: IRequestStatus) => (_state: IRequestStatus, { payload: _submitOrderFormStatus }: ReduxActions.Action<IRequestStatus>) => (status || IRequestStatus.IDLE);
export const setInteractFailureReducer = () => (state: Record<string, unknown>, { payload: interactBankFailureInfo }: ReduxActions.Action<Record<string, unknown>>) => ({ ...state, ...interactBankFailureInfo });
export const setFailureReducer = () => (state: Record<string, unknown>, { payload: interactBankFailureInfo }: ReduxActions.Action<Record<string, unknown>>) => ({ ...state, ...interactBankFailureInfo });

export const setCancelPreauthReducer = () => (state: Record<string, unknown>, { payload: cancelPreauthPayments }: ReduxActions.Action<Record<string, unknown>>) => ({ ...state, ...cancelPreauthPayments });
export const setCancelPreauthStatusReducer = (status: IRequestStatus) => (_state: IRequestStatus, { payload: _cancelPreauthStatus }: ReduxActions.Action<IRequestStatus>) => (status || IRequestStatus.IDLE);

export const setTokenizeAndPropagateFormValuesStatusReducer = (status: IRequestStatus) => (_state: IRequestStatus, { payload: _tokenizeAndPropagateFormValuesStatus }: ReduxActions.Action<IRequestStatus>) => (status || IRequestStatus.IDLE);
