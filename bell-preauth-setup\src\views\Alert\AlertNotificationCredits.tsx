import * as React from "react";
import {Icon, Card} from "@bell/bell-ui-library";
import { injectIntl } from "react-intl";

const variants = {
    greatNews: {
        className:"brui-text-15 brui-text-blue",
        iconClass:"bi_brui",
        iconName:"bi_tag_note-big",
    },
    notifCardWarning: {
        className:"brui-text-20 brui-text-yellow",
        iconClass:"bi_brui",
        iconName:"bi_error_bl_bg_cf",
    }
}

export interface NotificationCardProps {
    intl: any,
    hasNotifCard?: boolean,
    children: React.ReactNode,
    isGreatNewsCard?: boolean,
    isNotifCardWarning?: boolean,
    label: React.ReactNode,
    variant: "greatNews" | "notifCardWarning",
}

const NotificationCardComponent = ({intl, hasNotifCard = false, children, label, variant}:NotificationCardProps) => {
    const GREAT_NEWS_NOTE = intl.formatMessage({id: "ALERT_GREAT_NEWS_NOTE"});
    const GREAT_NEWS_NOTE_DESCRIPTION = intl.formatMessage({id: "ALERT_GREAT_NEWS_NOTE_DESC"});
    return (
        <Card variant="gray" radius={true} className={[
            "brui-flex brui-flex-col sm:brui-flex-row brui-p-15 brui-gap-15 brui-rounded-[16px]",
            hasNotifCard? "" : "brui-hidden"
          ].join(" ").trim()}>
                <div className="brui-flex brui-size-20 brui-items-start payment-pb-15 payment-pr-15">
                    <Icon 
                    className={["",variants[variant].className].join(" ").trim()}
                    iconClass={["",variants[variant].iconClass].join(" ").trim()}
                    iconName={["",variants[variant].iconName].join(" ").trim()}>
                    </Icon>
                </div>
                <div className="brui-flex-grow">
                    <p className="brui-text-14 brui-leading-18 brui-text-gray brui-mb-10">
                        {label}
                    </p>
                    {children}
                    <div className="brui-text-12 brui-text-gray brui-leading-14">
                        <strong>{GREAT_NEWS_NOTE}</strong>{GREAT_NEWS_NOTE_DESCRIPTION}
                    </div>
                </div>
          </Card>
    )
}

export const AlertNotificationCredits = injectIntl(NotificationCardComponent);