export interface SubscriberOffersWithBan {
    Ban: string;
    banInfo: BanInfo;
    AutopayEligibleSubscribers: AutopayEligibleSubscribers[];
}

export interface BanInfo {
    banId: string;
    nickName: string;
    autopayPaymentMethod: string;
}

export interface AutopayEligibleSubscribers {
    autopayOffers: AutopayOffers[];
    subscriberStatus: string;
    subscriberNumber: string;
    subscriberTelephoneNumber: string;
    errorCode: string;
}

export interface AutopayOffers {
    discountCode: string;
    incentiveSoc: string;
    action: string;
    actionDate: string;
    graceEligibleStatus: string;
    incentiveOfferDescription: string;
    eligibleCreditCardTypes: string;
    eligiblePaymentMethods: string;
    incentiveOfferCode: string;
    discountAmount: number;
    incentiveOfferAltDescription: string;
    discountGracePeriodStartDate: string;
    discountGracePeriodEndDate: string;
    currentDiscountAmount: number;
    currentdiscountAmount:number;
    offerImpact: string;
}

export default SubscriberOffersWithBan;