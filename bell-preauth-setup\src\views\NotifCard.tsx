import React from "react";
import { Icon, Card, Text} from "@bell/bell-ui-library";

const variants = {
  greatNews: {
    className:"brui-text-15 brui-text-blue",
    iconClass:"bi_brui",
    iconName:"bi_tag_note-big",
    
  },

  notifCardWarning: {
    className:"brui-text-20 brui-text-yellow",
    iconClass:"bi_brui",
    iconName:"bi_error_bl_bg_cf",
  },

  notifCardInfoAlert: {
    className:"brui-text-20 brui-text-blue",
    iconClass:"bi_brui",
    iconName:"bi_info_notif_small",
  }
}


export interface PaymentMethodProps {
  hasNotifCard?: boolean,
  children?: React.ReactNode,
  isGreatNewsCard?: boolean,
  isNotifCardWarning?: boolean,
  // isNoteExisting?: boolean,
  label?: React.ReactNode,
  label1?:React.ReactNode,
  label2?:React.ReactNode
  label3?:React.ReactNode,
  simpleLabel?: React.ReactNode,
  variant: "greatNews" | "notifCardWarning" | "notifCardInfoAlert",
}


export const NotifCard = ({hasNotifCard = false, children, label,label1,label2,label3, variant}:PaymentMethodProps) => {
  return (
    <Card variant="gray" radius={true} className={[
      "payment-flex payment-flex-col sm:payment-flex-row payment-p-15 payment-gap-15 payment-rounded-[16px] payment-mb-30",
      hasNotifCard? "" : "payment-hidden"
    ].join(" ").trim()}>
          <div className="payment-flex payment-size-20 payment-items-center payment-justify-center payment-self-start sm:payment-self-start ">
            <Icon 
              className={["",variants[variant].className].join(" ").trim()}
              iconClass={["",variants[variant].iconClass].join(" ").trim()}
              iconName={["",variants[variant].iconName].join(" ").trim()}>
            </Icon>
          </div>
          <div  id ={hasNotifCard? "discount-offer":""} className="payment-flex-grow">
            <p className="brui-text-14 brui-leading-18 brui-text-gray brui-mb-10">
              <span className="payment-font-bold payment-text-black">
              {label}&nbsp;
              </span>
              <span className="payment-text-black">
              {label1}
              </span>
            </p>
            {children}
        <div className="payment-text-12 payment-text-gray payment-leading-14"><strong>{label2}</strong>{label3}</div>
      </div>
    </Card>
  );
};

export const SimpleNotifCard = ({children, simpleLabel}:PaymentMethodProps) => {
  return (
    <Text role="alert" className="brui-bg-gray-3 brui-flex brui-flex-col sm:brui-flex-row brui-p-15 brui-gap-15 brui-rounded-[16px]" elementType="div">
      <div className="brui-flex brui-size-20 brui-items-center brui-justify-center">
          <Icon  className="brui-text-20 brui-text-blue" iconClass="bi_brui" iconName="bi_info_notif_small"></Icon>
      </div>
      <Text className="brui-text-14 brui-leading-20 brui-text-gray" elementType="p">{simpleLabel}</Text>
  </Text>
  );
};










