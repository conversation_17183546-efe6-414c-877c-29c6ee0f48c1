import React, { ComponentPropsWithRef, forwardRef } from "react";
import {<PERSON>Button, Heading, Divider} from "@bell/bell-ui-library";

export interface PaymentMethodProps extends ComponentPropsWithRef<"input"> {
    id: string;
    name: string;
    defaultChecked?: boolean;
    label: string;
    headingLevel?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
    children: React.ReactNode;
    ariaDescribe?:string;
}

export const PaymentRadioCard = forwardRef<HTMLInputElement, PaymentMethodProps>(function PaymentRadioCard(
    {
        id,
        name,
        defaultChecked,
        label,
        headingLevel="h4",
        children,
        ariaDescribe,
        onChange
    },
    ref
){
    return (
        <>
            <div className="brui-rounded-20 brui-border brui-border-gray-8 transition-all brui-mb-15 brui-drop-shadow-none has-[input[type=radio].brui-size-full:checked]:payment-shadow-3sm">
                <label className="payment-peer/paymentradio payment-group/paymentradio brui-flex brui-items-center brui-p-30 payment-pl-15 sm:payment-pl-30 brui-cursor-pointer">
                    <RadioButton
                        id={id}
                        name={name}
                        value={label}
                        variant="default"
                        defaultChecked={defaultChecked}
                        aria-describedby={ariaDescribe || undefined}
                        ref={ref}
                        onChange={onChange}
                    >
                        <div className="brui-relative brui-flex brui-justify-between">
                            <Heading
                                level={headingLevel}
                                variant="default"
                                id={id + "-label"}
                                className="brui-font-sans brui-mb-0 brui-text-16 brui-leading-20 brui-mt-3 sm:brui-mr-64 payment-text-gray group-has-[:checked]/paymentradio:payment-text-black"
                            >
                                {label}
                            </Heading>
                        </div>
                    </RadioButton>
                </label>
                <div className="payment-hidden peer-has-[input[type=radio]:checked]/paymentradio:payment-block payment-px-15 payment-pb-30 sm:payment-px-30">
                    <Divider direction="horizontal" width={1} className="brui-mb-30 brui-bg-gray-4" />
                    {children}
                </div>
            </div>
        </>
    )
});

export default PaymentRadioCard;