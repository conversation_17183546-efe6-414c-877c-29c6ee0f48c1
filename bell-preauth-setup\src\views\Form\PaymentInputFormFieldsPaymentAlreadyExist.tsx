import React, { ComponentPropsWithRef, forwardRef } from "react";
import { injectIntl } from "react-intl";
import { FormFieldsBankRadioPayment, BankPaymentRadioButton } from "./PaymentInputFormFieldsBankPaymentRadio";
import { PaymentItem } from "../../models";
import { getPaymentItemCardType } from "../../utils/PaymentItemUtils";



interface PaymentInputFormFieldsPaymentAlreadyExistProps extends ComponentPropsWithRef<"input"> {

  intl: any;
  isCreditCardPayment?: boolean;
  isBankPayment?: boolean;
  isPreAuth?: boolean;
  paymentItems: PaymentItem[];
  children?: React.ReactNode;
  getExistingBankPaymentDetails?: Function;
}

const PaymentInputFormFieldsPaymentAlreadyExistComponent = forwardRef <HTMLInputElement,PaymentInputFormFieldsPaymentAlreadyExistProps> (function PaymentInputFormFieldsPaymentAlreadyExistComponent({
  intl,
  isCreditCardPayment = false,
  isBankPayment = false,
  isPreAuth = false,
  paymentItems,
  onChange,
  getExistingBankPaymentDetails
},forwardedRef ){
  const legendStr = isCreditCardPayment
    ? intl.formatMessage({id:"EXISTING_CC_TITLE"})
    : isBankPayment
    ? intl.formatMessage({id:"EXISTING_BANK_TITLE"})
    : null;
  const srOnlyStr  = `${intl.formatMessage({id: "SELECT_REQUIRED_LEGEND"})} ${legendStr} `;
  return (
    <>
    {isCreditCardPayment ? (
      <>
        <FormFieldsBankRadioPayment isCreditCardPayment legends={legendStr} srOnly={srOnlyStr}>
          {paymentItems.filter((x) => x.IsOnPreauthorizedPayments && x.CreditCardDetails != null).map((items, index) => (
            items.CreditCardDetails && (
              <BankPaymentRadioButton idPrefix="PACC" name="credit-card-radio"
              value={getPaymentItemCardType(items.CreditCardDetails.CreditCardType)}
              label={`${getPaymentItemCardType(items.CreditCardDetails.CreditCardType) }<br>************${items.CreditCardDetails.CreditCardNumberMasked}, ${intl.formatMessage({id: "CREDIT_CARD_VALID"})} ${items.CreditCardDetails.ExpireYear}`}
              defaultChecked={isPreAuth ? true : undefined} 
              ref={forwardedRef}
              onChange={onChange}
              />  
            )
          ))}
          <BankPaymentRadioButton idPrefix="PACC" name="credit-card-radio" value={intl.formatMessage({id: "NEW_CREDIT_ACCOUNT_LABEL"})}  
           label={intl.formatMessage({id: "NEW_CREDIT_ACCOUNT_LABEL"})}   
           onChange={onChange} 
           defaultChecked={isPreAuth ? false : undefined} />
          
        </FormFieldsBankRadioPayment>
      </>
    ) : isBankPayment ? (
      <>
        <FormFieldsBankRadioPayment isBankPayment legends={legendStr} srOnly="srOnlyStr">
          {paymentItems.filter((x) => x.IsOnPreauthorizedPayments && x.BankAccountDetails != null).map((items, index) => (
            items.BankAccountDetails && (
              <BankPaymentRadioButton idPrefix="PAD" name="bank-card-details-radio"
              value={items.BankAccountDetails.BankName}
              label={`${items.BankAccountDetails.BankName}<br>(${items.BankAccountDetails.AccountNumberMaskedDisplayView})`}
              defaultChecked={index <= 0 ? true : undefined} 
              ref={forwardedRef}
              onChange={onChange}
              getExistingBankPaymentDetails={getExistingBankPaymentDetails}
              paymentDetails={new Array(items)}
              />  
            )
          ))}
          <BankPaymentRadioButton idPrefix="PAD" name="bank-card-details-radio" value={intl.formatMessage({id: "BANK_NEW_BANK_ACCOUNT_LABEL"})} 
          label={intl.formatMessage({id: "BANK_NEW_BANK_ACCOUNT_LABEL"})} 
          onChange={onChange}
          getExistingBankPaymentDetails={getExistingBankPaymentDetails} />
        </FormFieldsBankRadioPayment>
      </>
    ) : null}
    </>
  );
});

export const PaymentInputFormFieldsPaymentAlreadyExist = injectIntl(PaymentInputFormFieldsPaymentAlreadyExistComponent);