import * as React from "react";
import * as ReactDOM from "react-dom";
import { WidgetLoader, Init } from "bwtk";


export function initialize(config: any, root: string, debug: any) {
  config = { ...config, "loader.staticWidgetMappings": {
    "bell-preauth-setup": {
      factory: () => require("bell-preauth-setup"),
      namespace: "Preauth/Setup"
    }
  }};
  Init(config);

  ReactDOM.render(
    <WidgetLoader widget="bell-preauth-setup" />,
    document.getElementById(root));
}
