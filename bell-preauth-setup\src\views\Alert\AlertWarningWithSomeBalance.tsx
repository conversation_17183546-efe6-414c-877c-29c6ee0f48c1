import * as React from "react";
import {<PERSON><PERSON>, <PERSON>ing, Button, Text, Price} from "@bell/bell-ui-library";
import { injectIntl } from "react-intl";
import { AccountInputValues, PaymentItem } from "../../models";




interface AlertNotificationMultiProps {
    intl: any;
    multiban: boolean;
    submitMultiOrderPayment: any;
    accountInputValue: AccountInputValues[];
    paymentItem: PaymentItem[];
    language: "en" | "fr";

} 

export const AlertWarningWithSomeBalancesPaidMulti = injectIntl(({
    intl,
    multiban,
    submitMultiOrderPayment,
    accountInputValue, 
    paymentItem,
    language
}:AlertNotificationMultiProps) =>  {
    
    let amountDue = 0.00;
    if(!multiban){
        const accountNumber = accountInputValue
            .filter((x) => x.transactionID === submitMultiOrderPayment[0]?.OrderFormId)
            .map((x) => x.accountNumber)[0]; 

        if (accountNumber) {
            const amountDueItem = paymentItem
            .filter((x) => x.Ban === accountNumber)
            .map((x) => x.Due)[0]; 

            if (amountDueItem) {
                amountDue = amountDueItem;
            }
        }
    }
    return (
        <Alert
        variant="warning" 
        className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-20 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block" 
        iconSize="36">
            <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0">
                { multiban &&(
                    <>
                    <Heading level="h2" variant="xs" className="payment-mb-15 payment-font-sans payment-font-bold">
                        {intl.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCE_MULTI"})}
                    </Heading>
                    <p className="payment-text-14 payment-mb-10 payment-text-gray">                            
                        {intl.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCES_DESC_MULTI"})}              
                    </p>
                    <Text elementType="div" className="payment-mb-30 payment-mt-15">
                        {
                        
                            submitMultiOrderPayment.map((item: any, index: any) => {
                                // Define the variable to store the filtered account number
                                const accountNumber = accountInputValue
                                    .filter((x) => x.transactionID === item.OrderFormId && x.payBalanceAmnt === 0)
                                    .map((x) => x.accountNumber)[0];

                                const amountDue = paymentItem
                                    .filter((x) => x.Ban === accountNumber)
                                    .map((x) => x.Due)[0];

                                if (amountDue === 0) {
                                    return
                                }
                            
                                return (
                                    <>
                                        {(
                                            <Text elementType="div" className="payment-flex payment-justify-between sm:payment-justify-normal">
                                            <label className="payment-text-14 sm:payment-basis-1/4">
                                                <strong>{accountNumber}</strong>
                                            </label>
                                            <Price 
                                                language={language}
                                                showZeroDecimalPart
                                                price={amountDue}
                                                variant="defaultPrice"
                                                className="!payment-text-14 payment-leading-14 payment-m-5 payment-font-normal"/>
                                            </Text>     
                                        )}
                                    </>
                                );
                            })
                        }   
                    </Text>   
                    </>
                )}
                {!multiban && (
                    <>
                        <Heading level="h2" variant="xs" className="payment-mb-15 payment-font-sans payment-font-bold">
                        {intl.formatMessage({ id: "ALERT_CONFIRMATION_OPTED_SOME_BALANCE_SINGLE" })}
                        </Heading>

                        <p className="payment-text-14 payment-mb-10 payment-text-gray">
                        {intl.formatMessage({ id: "ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_1" })}
                        <Price
                            language={language}
                            price={Number(amountDue)} // Now amountDue is available in the outer scope
                            variant="defaultPrice"
                            className="!payment-text-14 payment-leading-14 brui-m-5 payment-font-normal brui-inline-block"
                        />
                        {intl.formatMessage({ id: "ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_2" })}
                        </p>
                    </>
                  )}
                <Button 
                    variant="primary" 
                    size="regular"
                    onClick={() => {
                            location.href = `${intl.formatMessage({id:"CTA_MAKE_PAYMENT_LINK"})}`;
                        }}
                >               
                    {intl.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"})}     
                </Button>
            </Text>
        </Alert>
  );
})
