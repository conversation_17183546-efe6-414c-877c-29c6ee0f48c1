import { CreditCardInfo } from "../models/CreditCardInfo";

export default function prepareStateData(creditCardInfo: CreditCardInfo): CreditCardInfo {
    if (creditCardInfo.cardNumberToken && creditCardInfo.cardNumberToken.length > 0) {
        if (creditCardInfo.cardNumberToken.length > 16){
            creditCardInfo.maskdCardNumber = creditCardInfo.creditCardNumber? creditCardInfo.creditCardNumber.replace(/\d(?=\d{4})/g, "*") : "";
        }
        else{
            creditCardInfo.maskdCardNumber = creditCardInfo.cardNumberToken.replace(/\d(?=\d{4})/g, "*");
        }
    }
    else {
        creditCardInfo.maskdCardNumber = "";
    }
    if (creditCardInfo.expiration && creditCardInfo.expiration.indexOf("/") > -1) {
        creditCardInfo.expirationMonth = creditCardInfo.expiration.split("/")[0];
        creditCardInfo.expirationYear = creditCardInfo.expiration.split("/")[1];
    }
    return creditCardInfo;
}