import React, { forwardRef }  from "react";
import {CheckboxCard, Text, Price}from "@bell//bell-ui-library";
import { PaymentItem } from "../../models";

export interface CurrentBalanceProps extends React.ComponentPropsWithoutRef<"div"> {
  isDisabled?: boolean;
  isChecked?: boolean;
  label?: string;
  id?: string;
  billType?: string;
  billAccountNumber?: string;
  idIndex?: number;
  text?: React.ReactNode;
  priceSettings?: {
    price: number;
    language?: "en" | "fr";
    negativeIndicator?: "CR" | "-" | null;
  };
  currentItem: PaymentItem
  isCheckedBalanceItems: PaymentItem[];
  setIsCheckedBalanceItems: Function;
  intl:any;
}
export const CheckboxCardCurrentBalance = forwardRef<HTMLInputElement, CurrentBalanceProps> (({
  className,
  isDisabled,
  isChecked,
  label,
  id,
  billType,
  billAccountNumber,
  idIndex,
  text,
  priceSettings,
  currentItem,
  isCheckedBalanceItems,
  setIsCheckedBalanceItems,
  intl
}: CurrentBalanceProps, ref) => {

  const handleCheckboxChange = (event: any, item: PaymentItem) => {
    if (event.target.checked)
    {
      setIsCheckedBalanceItems([...isCheckedBalanceItems, item]);
    }
    else
    {
      setIsCheckedBalanceItems((isCheckedItems: any) => isCheckedItems.filter((checkedItem: any) => checkedItem.BillName !== item.BillName))
    }
  };
  const  PayMyBalanceSR = priceSettings ? intl.formatMessage({id:"PAY_MY_BALANCE_SR"},{balance:priceSettings.price}): undefined; ;

  return (
    <CheckboxCard
      ref={ref}
      id={id}
      aria-labelledby={label}
      disabled={isDisabled}
      defaultChecked={isChecked}
      className={["group-has-[:disabled]/inputcheckbox:payment-bg-gray-3 payment-chekcbox-disabled payment-group/checkboxcard payment-pt-30 payment-pl-30 brui-pr-30 payment-pb-30 sm:payment-p30 payment-basis-unset sm:payment-basis-p48 md:payment-basis-p35 brui-w-full payment-mr-15 brui-mb-15", className].join(" ").trim()}
      defaultPadding={false}
      checkboxPlacement="topLeft"
      data-banDetail={JSON.stringify({id,billAccountNumber, billType, price: priceSettings?.price})}
      onChange={(e) => handleCheckboxChange(e, currentItem)}
    >
      <div className="sm:payment-flex sm:payment-pl-[33px] payment-pl-[24px]">
        <div
          className="payment-mt-2 sm:payment-mt-1 payment-flex-1 sm:payment-text-left"
          id={`checkboxBalance-${idIndex}-label-${idIndex}`}
        >
          <Text
            aria-hidden="true"
            elementType="span"
            className="sm:payment-pl-5 payment-text-16 sm:payment-text-18 payment-leading-20 sm:payment-leading-22 brui payment-font-bold payment-flex payment-flex-row payment-items-center payment-gap-5"
          >
            {text}
            {priceSettings && (
              <Price
                language={priceSettings.language ? priceSettings.language : "en"}
                negativeIndicator={priceSettings.negativeIndicator ? priceSettings.negativeIndicator : "CR"}
                price={priceSettings.price ? priceSettings.price : 0.0}
                variant="defaultPrice"
                className="!payment-text-[16px] !sm:payment-text-[18px] payment-leading-20 sm:payment-leading-22"
              />
            )}
          </Text>
          {PayMyBalanceSR &&<span className="brui-sr-only">{PayMyBalanceSR}</span>}
        </div>
        <div
          className="payment-mt-2 sm:payment-mt-1 payment-flex-2 sm:payment-text-right payment-leading-18"
          id={`checkboxBalance-${idIndex}-label-${idIndex}-info`}
        >
          <Text elementType="span" className="payment-text-gray payment-text-14 sm:payment-text-14">
            {" "}
            on
            <Text elementType="span" className="payment-font-bold payment-text-black">
              {" "}
              {billType}{" "}
            </Text>
            <span aria-hidden="true">{billAccountNumber}</span>
            <span className="payment-sr-only">{billAccountNumber?.split('').join(' ')}</span>
          </Text>
        </div>
      </div>
    </CheckboxCard>
  )
}
);

