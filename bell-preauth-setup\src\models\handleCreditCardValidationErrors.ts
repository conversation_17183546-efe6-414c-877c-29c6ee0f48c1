import { FieldType } from "./Enums";
import { InputErrorTypes, IValidationErrors, ValidationErrors } from "./Error";

export function validateCreditCardNumber(creditcardNumber: string): any {
    let validationErrors = new ValidationErrors();
    validationErrors.errors = new Array<IValidationErrors>();
    let errors = new Array<InputErrorTypes>;
    
    if (!creditcardNumber){
        errors.push(InputErrorTypes.Empty)
    }

    if (errors && errors.length > 0) {
        validationErrors.errors.push({valErrors: errors, field: FieldType.CardNumber});
        return validationErrors;
    } else {
        return [];
    }
}

export function validateCardHolderName(cardholderName: string): any {
    let validationErrors = new ValidationErrors();
    validationErrors.errors = new Array<IValidationErrors>();
    let errors = new Array<InputErrorTypes>;
    const validationString = /^(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{2,}\.?\s)+(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{1}\.?\s)*[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{2,}\.?$/;
    // regex rules
    // -Minimum if 5 Characters (2 first name, 2 lastname and 1 space)
    // -web will accept all alternate characters e.g è,é,à,â,ê,ë
    // -web will accept hyphen "-" and " ' "
    // -system will accept a maximum of 70 characters
    
    if (!cardholderName){
        errors.push(InputErrorTypes.Empty)
    } else {
      const accountHolderIsValid = 
        validationString.test(cardholderName.trim()) 
        && cardholderName.trim().length <= 70;// -sy
      
      if (!accountHolderIsValid) {
        errors.push(InputErrorTypes.Empty);
      } 
    }

    if (errors && errors.length > 0) {
        validationErrors.errors.push({ valErrors: errors, field: FieldType.CardHolderName });
        return validationErrors;
    } else {
        return [];
    }
}
export function validatBankDetailsErrorMapper(ValidationFormObj: any): any {
  let validationErrors = new ValidationErrors();
  validationErrors.errors = new Array<IValidationErrors>();
  var ValidationForm = ValidationFormObj.validationForm;
  if (ValidationForm.bankNameError) {
    if (ValidationForm.bankNameError.isEmpty) {
      validationErrors.errors.push({ valErrors: [InputErrorTypes.Empty], field: FieldType.BankName });
    }
    if (ValidationForm.bankNameError.isInvalid) {
      validationErrors.errors.push({ valErrors: [InputErrorTypes.Invalid], field: FieldType.BankName });
    }
  }
  // do same for other bank types
  if (ValidationForm.bankAccountHolderError) {
    if (ValidationForm.bankAccountHolderError.isEmpty) {
      validationErrors.errors.push({ valErrors: [InputErrorTypes.Empty], field: FieldType.BankAccountHolderName });
    }
    if (ValidationForm.bankAccountHolderError.isInvalid) {
      validationErrors.errors.push({ valErrors: [InputErrorTypes.Invalid], field: FieldType.BankAccountHolderName });
    }
  }

  if (ValidationForm.transitNumberError) {
    if (ValidationForm.transitNumberError.isEmpty) {
      validationErrors.errors.push({ valErrors: [InputErrorTypes.Empty], field: FieldType.BankTransitCode });
    }
    if (ValidationForm.transitNumberError.isInvalid) {
      validationErrors.errors.push({ valErrors: [InputErrorTypes.Invalid], field: FieldType.BankTransitCode });
    }
  }

  if (ValidationForm.bankAccountNumberError) {
    if (ValidationForm.bankAccountNumberError.isEmpty) {
      validationErrors.errors.push({ valErrors: [InputErrorTypes.Empty], field: FieldType.BankAccountNumber });
    }
    if (ValidationForm.bankAccountNumberError.isInvalid) {
      validationErrors.errors.push({ valErrors: [InputErrorTypes.Invalid], field: FieldType.BankAccountNumber });
    }
  }
  if (!ValidationFormObj.isValid) {
    return validationErrors
  }
  return [];
}

export function validateSecurityCode(securitycode: string): any {
    let validationErrors = new ValidationErrors();
    validationErrors.errors = new Array<IValidationErrors>();
    let errors = new Array<InputErrorTypes>;
    
    if (!securitycode){
        errors.push(InputErrorTypes.Empty)
    }

    if (errors && errors.length > 0) {
        validationErrors.errors.push({ valErrors: errors, field: FieldType.SecurityCode });
        return validationErrors;
    } else {
        return [];
    }
}

export function validateCreditCardExpiryDate(expirymonth: any, expiryyear: any):any {
    let validationErrors = new ValidationErrors();
    validationErrors.errors = new Array<IValidationErrors>();
    let errors = new Array<InputErrorTypes>
    let year: number = expiryyear=="" ? expiryyear : parseInt(expiryyear);
    let month: number = expirymonth=="" ? expirymonth : parseInt(expirymonth);

    if (month >= 12) {
        month = 0;
        year += 1;
    }
    const fullYear = year <= 49 ? 2000 + year : 1900 + year;

    let selectedDate = new Date(fullYear, month, 0);
    if (year.toString() == "" || month.toString() == "") {
        errors.push(InputErrorTypes.Empty);
    } else if (selectedDate < new Date()) {
        errors.push(InputErrorTypes.CreditCardExpireDate);
    }

    if (errors && errors.length > 0) {
        validationErrors.errors.push({ valErrors: errors, field: FieldType.ExpirationDate });
        return validationErrors;
    } else {
        return [];
    }
}

export function getValError(validationError: ValidationErrors, fieldType: FieldType): any {
    let valError: string = validationError.errors.filter(e => e.field.includes(fieldType))[0].valErrors[0];
    let hasError: boolean = false;
    hasError = valError.includes(InputErrorTypes.Empty) ? true : false;
    return hasError;
}
