import * as React from "react";
import { injectIntl } from "react-intl";
import { SingleRowInformation } from "../SingleRowInformation";
//import { SummaryInformationHeading } from "../SummaryInformationHeading";
import { HeadingStep, Icon, IconLink,Text} from "@bell/bell-ui-library";
import { CreditCardInputValue, PaymentItem, InputBankAccountDetail, SelectListItem, SubscriberOffersWithBan} from "../../models";
import { getPaymentItemCardType } from "../../utils/PaymentItemUtils";

interface PaymentSummaryComponentProps {
    intl: any;
    className:any;
    paymentItem: PaymentItem[];
    isPreauth:boolean;
    isNewbank:boolean;
    isSingleClickEnable?:boolean;
    inputValue: CreditCardInputValue
    inputBankValue?: InputBankAccountDetail;
    isBankPaymentSelected: boolean;
    showHeading: boolean;
    onEditClick?: () => void;
    bankList: SelectListItem[];
    checkedBillItems: PaymentItem[];
  creditCardAutopayOffers:SubscriberOffersWithBan[];
  debitCardAutopayOffers:SubscriberOffersWithBan[];
  bankitems: any[];
  isConfirmation ?:boolean;
  IsAutopayCreditEnabled?: boolean;
  isShow?:boolean;
}

const PaymentSummaryComponent = ({ intl, className ,paymentItem, isPreauth, inputValue, inputBankValue, isBankPaymentSelected, isNewbank, onEditClick, showHeading,isSingleClickEnable,bankList,debitCardAutopayOffers,creditCardAutopayOffers,checkedBillItems,bankitems,isConfirmation,IsAutopayCreditEnabled,isShow}:PaymentSummaryComponentProps) => {
  const checkedBanOffers = () => {
    var filteredOffer: any = [];
    !isBankPaymentSelected ?
    creditCardAutopayOffers && creditCardAutopayOffers?.map((item) => {
      checkedBillItems && checkedBillItems?.map((billItem) => {
        if (item.Ban == billItem.Ban) {
          filteredOffer.push(item);
        }
      });
    }):
    debitCardAutopayOffers && debitCardAutopayOffers?.map((item) =>{
      checkedBillItems && checkedBillItems.map((billItem)=>{
       if(item.Ban == billItem.Ban){
         filteredOffer.push(item);
       }
      });
  });
    return filteredOffer;
  }
  const getTotalOffers = () => {
     
    return bankitems && bankitems.length > 1 ? checkedBanOffers().reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
     
     const offersCount = ban.AutopayEligibleSubscribers.reduce(
       (count, subscriber) => count + subscriber.autopayOffers.length,
       0
     );
     return total + offersCount;
   }, 0): !isBankPaymentSelected ? creditCardAutopayOffers && creditCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
     
     const offersCount = ban.AutopayEligibleSubscribers.reduce(
       (count, subscriber) => count + subscriber.autopayOffers.length,
       0
     );
     return total + offersCount;
   }, 0): debitCardAutopayOffers && debitCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
     
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0);
 };
    const filteredItems = paymentItem.filter(
    item => item.IsOnPreauthorizedPayments === true && item.CreditCardDetails
    );
    const CreditCardDetails =  filteredItems.length > 0 ? filteredItems[0].CreditCardDetails : null;
    console.log('inputbankvalue', inputBankValue);
    const PaymentInformationItems = () => {
        if (isNewbank || isPreauth === false){
            if ( isBankPaymentSelected == true) {
              return [
                {
                  label: intl.formatMessage({id:"PAYMENT_METHOD"}),
                  value: inputBankValue?.PaymentMethod,
                },
                {
                  label: intl.formatMessage({id:"ACCOUNT_HOLDER"}),
                  value: inputBankValue?.AccountHolder,
                },
                {
                  label: intl.formatMessage({id:"BANK_NAME"}),
                  value: bankList.filter(x=> x.Value == inputBankValue?.BankName)[0]?.Text,
                },
                {
                  label: intl.formatMessage({id:"TRANSIT_NUMER"}),
                  value: inputBankValue?.TransitNumber,
                },
                {
                  label: intl.formatMessage({id:"ACCOUNT_NUMBER"}),
                  value: inputBankValue?.AccountNumber ? `*******${String(inputBankValue.AccountNumber).slice(-3)}`
                      : inputBankValue?.AccountNumber,
                },
                
              ];
            } else {
              return [
                {
                  label: intl.formatMessage({ id: "CREDIT_CARD_TYPE_LABEL" }),
                  value: inputValue.cardType,
                },
                {
                  label: intl.formatMessage({ id: "CREDIT_CARD_NUMBER_LABEL_V2" }),
                  value:
                    isNewbank && inputValue.cardNumber
                      ? `*******${String(inputValue.cardNumber).slice(-4)}`
                      : inputValue.cardNumber,
                },
                {
                  label: intl.formatMessage({ id: "CREDIT_CARD_NAME_LABEL_V2" }),
                  value: inputValue.cardName,
                },
                {
                  label: intl.formatMessage({ id: "CREDIT_CARD_EXPIRY_LABEL" }),
                  value: inputValue.expiryDate,
                },
              ];
            }
            
        } else if (isNewbank === false || isPreauth === true) { // New Creditcard bank Details
          if(isBankPaymentSelected == true) {
            return [
              {
                label: intl.formatMessage({id:"PAYMENT_METHOD"}),
                value: inputBankValue?.PaymentMethod,
              },
              {
                label: intl.formatMessage({id:"ACCOUNT_HOLDER"}),
                value: inputBankValue?.AccountHolder,
              },
              {
                label: intl.formatMessage({id:"BANK_NAME"}),
                value: inputBankValue?.BankName,
              },
              {
                label: intl.formatMessage({id:"TRANSIT_NUMER"}),
                value: inputBankValue?.TransitNumber,
              },
              {
                label: intl.formatMessage({id:"ACCOUNT_NUMBER"}),
                value: inputBankValue?.AccountNumber ? `*******${String(inputBankValue.AccountNumber).slice(-3)}`
                    : inputBankValue?.AccountNumber,
              },
              
            ];
          } else if(inputValue){
            return [
              {
                label: intl.formatMessage({ id: "CREDIT_CARD_TYPE_LABEL" }),
                value: inputValue.cardType,
              },
              {
                label: intl.formatMessage({ id: "CREDIT_CARD_NUMBER_LABEL_V2" }),
                value:
                  isNewbank && inputValue.cardNumber
                    ? `*******${String(inputValue.cardNumber).slice(-4)}`
                    : inputValue.cardNumber,
              },
              {
                label: intl.formatMessage({ id: "CREDIT_CARD_NAME_LABEL_V2" }),
                value: inputValue.cardName,
              },
              {
                label: intl.formatMessage({ id: "CREDIT_CARD_EXPIRY_LABEL" }),
                value: inputValue.expiryDate,
              },
            ];
          }else if(CreditCardDetails){ // From Preauth Credicard Details
                return [
                {
                    label: intl.formatMessage({id:"CREDIT_CARD_TYPE_LABEL"}),
                    value: getPaymentItemCardType(CreditCardDetails.CreditCardType)
                },
                {
                    label: intl.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL_V2"}),
                    value: CreditCardDetails?.CreditCardNumberMasked
                },
                {
                    label: intl.formatMessage({id:"CREDIT_CARD_NAME_LABEL_V2"}),
                    value: CreditCardDetails?.CardholderName
                },
                {
                    label: intl.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"}),
                    value: CreditCardDetails?.ExpirationDateDisplayViewA
                },
                ];
            }
        }
        return [];

    };
    
    return (
        <div className={className} >
            <div className={isConfirmation ? "payment-border-gray-4" : "payment-border-b payment-border-gray-4"}>
                <div className={showHeading ? "payment-flex payment-items-center payment-justify-between payment-mt-0" : "payment-hidden"} >
                    <HeadingStep
                    disableSrOnlyText={true}
                    status="complete"
                    subtitle=""
                    hideSubtitle
                    variant="leftAlignNoStep"
                    title={intl.formatMessage({id:"PAYMENT_SUMMARY_TITLE"}) }  
                    id={isShow ? "payment-setup-heading" : undefined}
                    aria-hidden={isShow ? "true" : undefined}

                    />
                    {/* show IconLink component on Review */}
                    <div className="payment-pt-45">
                        <IconLink
                            icon={<Icon iconClass="bi_brui" iconName="bi_edit_pencil" className="brui-text-16"></Icon>}
                            text={intl.formatMessage({id:"CTA_EDIT"})}
                            variant="textBlue"
                            size="regular"
                            href="javascript:void(0);"
                            position="right"
                            className={["payment-flex payment-items-center !payment-text-14 !payment-leading-18"].join(" ").trim()}
                            aria-describedby="pre-auth-payment-summary"
                            onClick={onEditClick} // Pass the click event
                            >
                        </IconLink>
                    </div>
                </div>
                {!isConfirmation && IsAutopayCreditEnabled &&
                getTotalOffers() >0?
                <Text elementType="div" className="payment-flex payment-items-center payment-mt-5">
                  <Icon className="paymnet-text-15 payment-text-blue payment-mr-5" iconClass="bi_brui" iconName="bi_tag_note-big"></Icon>
                  <p className="payment-text-gray payment-text-14">{intl.formatMessage({ id: "REVIEW_PAGE_AUTOPAY_CREDIT" })} </p>
                </Text>
				:null}
                {isBankPaymentSelected ?
                <div className={isConfirmation ? "payment-block payment-relative" : "payment-block payment-relative payment-pb-45"}>
                <div className="brui-mt-15">
                    {/* NOTE: add role=list when items is more than one */}
                    {PaymentInformationItems().map((item, index) => 
                    <SingleRowInformation 
                        className=""
                        label={item.label} 
                        value={item.value}
                        data-index={index} 
                        srText={item.label === intl.formatMessage({ id:"ACCOUNT_NUMBER"}) ? intl.formatMessage({ id:"BANK_ACCOUNT_SR_TEXT"},{Account:String(item.value).slice(-3)}) : ""}
                        needSRText={item.label === intl.formatMessage({ id:"ACCOUNT_NUMBER"}) ? true : false}
                      />)}
                </div>
            </div>
            :
                <div className={isConfirmation ? "payment-block payment-relative" : "payment-block payment-relative payment-pb-45"}>
                    <div className="brui-mt-15">
                        {/* NOTE: add role=list when items is more than one */}
                        {PaymentInformationItems().map((item, index) => 
                        <SingleRowInformation 
                            className=""
                            label={item.label} 
                            value={
                              item.label === intl.formatMessage({ id:"CREDIT_CARD_NUMBER_LABEL_V2"}) ? `*******${String(item.value).slice(-4)}` 
                                : item.value
                            }
                            data-index={index} 
                            srText={item.label === intl.formatMessage({ id:"CREDIT_CARD_NUMBER_LABEL_V2"}) ? intl.formatMessage({ id:"CREDIT_CARD_SR_TEXT"},{Account:String(item.value).slice(-4)}) : ""}
                            needSRText={item.label === intl.formatMessage({ id:"CREDIT_CARD_NUMBER_LABEL_V2"}) ? true : false}
                             />)}
                    </div>
                </div>
}
            </div>
                
        </div>
    );
}


export const PaymentSummary  = injectIntl(PaymentSummaryComponent)



