import * as React from "react";
import * as ReactDOM from "react-dom";
import { WidgetLoader, Init } from "bwtk";


export function initialize(config: any, root: string, debug: any) {
  config = { ...config, "loader.staticWidgetMappings": {
    "bell-preauth-manage": {
      factory: () => require("bell-preauth-manage"),
      namespace: "Preauth/Manage"
    }
  }};
  Init(config);

  ReactDOM.render(
    <WidgetLoader widget="bell-preauth-manage" />,
    document.getElementById(root));
}
