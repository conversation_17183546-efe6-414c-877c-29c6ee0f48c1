[{"C:\\Users\\<USER>\\WebApplications\\AP-SMB\\bell-preauth-manage-bundle\\src\\index.tsx": "1"}, {"size": 512, "mtime": 1750708153636, "results": "2", "hashOfConfig": "3"}, {"filePath": "4", "messages": "5", "suppressedMessages": "6", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "18cnq64", "C:\\Users\\<USER>\\WebApplications\\AP-SMB\\bell-preauth-manage-bundle\\src\\index.tsx", ["7", "8", "9"], [], {"ruleId": "10", "severity": 1, "message": "11", "line": 6, "column": 36, "nodeType": "12", "messageId": "13", "endLine": 6, "endColumn": 39, "suggestions": "14"}, {"ruleId": "10", "severity": 1, "message": "11", "line": 6, "column": 62, "nodeType": "12", "messageId": "13", "endLine": 6, "endColumn": 65, "suggestions": "15"}, {"ruleId": "16", "severity": 2, "message": "17", "line": 7, "column": 3, "nodeType": "18", "messageId": "19", "endLine": 7, "endColumn": 9}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["20", "21"], ["22", "23"], "no-param-reassign", "Assignment to function parameter 'config'.", "Identifier", "assignmentToFunctionParam", {"messageId": "24", "fix": "25", "desc": "26"}, {"messageId": "27", "fix": "28", "desc": "29"}, {"messageId": "24", "fix": "30", "desc": "26"}, {"messageId": "27", "fix": "31", "desc": "29"}, "suggestUnknown", {"range": "32", "text": "33"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "34", "text": "35"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "36", "text": "33"}, {"range": "37", "text": "35"}, [156, 159], "unknown", [156, 159], "never", [182, 185], [182, 185]]