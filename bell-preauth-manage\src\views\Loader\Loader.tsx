import * as React from 'react';
import { injectIntl } from 'react-intl';

interface loaderProps {
  intl: any;
  variant?: "default" | "submitOrder"
}


const LoaderComponent = ({ intl, variant = "default" }: loaderProps) => (
  <div className="payment-bg-black payment-bg-opacity-60 payment-fixed payment-w-full payment-h-full  payment-z-20 payment-left-0 payment-top-0 payment-inline-flex payment-items-center payment-justify-center">
    <div id="brf-page-loader" role="alert" aria-busy="true" aria-live="assertive" className="payment-inline-flex payment-items-center payment-py-15 payment-px-30 payment-shadow-md payment-bg-white">
      <svg className="payment-animate-spin payment-size-[36px] payment-mr-10" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="3 3 42 42">
        <defs>
          <linearGradient id="loadingIndicatorGradient1" x1="0" x2="0" y1="10%" y2="90%"><stop offset="0" stop-color="#04569b"></stop><stop offset="1" stop-color="#97b6d2"></stop></linearGradient>
          <linearGradient id="loadingIndicatorGradient2" x1="0" x2="0" y1="90%" y2="10%"><stop offset="0" stop-color="#97b6d2"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient>
        </defs>
        <path fill="url(#loadingIndicatorGradient1)" d="M24,3C12,3,3,12,3,24s9,21,21,21l-0.1-2.5c-5.3,0-10.1-2.2-13.5-6C7.4,33.1,5.5,28.3,5.5,24
        c0-4.4,1.9-9.5,5.3-12.9c3.5-3.6,8.6-5.6,13.2-5.6L24,3z"></path>
        <path fill="url(#loadingIndicatorGradient2)" d="M24,3l0,2.4c5.5,0,10.8,2.8,14.3,6.8c2.8,3.4,4.2,7.6,4.2,11.7c0,4.7-2,9.7-5.7,13.3c-3.3,3.3-8.1,5.3-12.9,5.3
        l0,2.5c12,0,21-10,21-21S36,3,24,3z"></path>
      </svg>
      {variant === "default" &&
                    <>{intl.formatMessage({ id: "LOADER" })}</>   
      }

      {variant === "submitOrder" &&
                    <div className="payment-text-12 payment-leading-14">
                      <p className="payment-font-bold">{intl.formatMessage({ id: "LOADER_SUBMIT" })}</p>
                      <p>{intl.formatMessage({ id: "LOADER_SUBMIT_DESC" })}</p>
                    </div> 
      }
    </div>
  </div>
);

export default injectIntl(LoaderComponent);
