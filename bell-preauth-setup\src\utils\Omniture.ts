// s_oPYM:bank, Credit
// s_oCCDT:credit card type
import { PaymentItem } from "../models"; // Ensure this path is correct or update it to the correct path

 export const getCardType = (cardtype: string) => {
  if(cardtype == "MASTERCARD") return "MC";
  if(cardtype == "AMEX") return "AX";
  if(cardtype == "VISA") return "VI";
  return cardtype;
}

export const getConfirmationOmniture = (
  isBankpaymentSelscted: boolean,
  showCurrentBalance: boolean,
  checkedCurrentBalanceItems: PaymentItem[],
  paymentItem: PaymentItem[],
  language:string,
  submitMultiOrderPayment: any,
  CreditCardType?: string,
) => {
  
  if (isBankpaymentSelscted) {
    if (showCurrentBalance) {
      if (checkedCurrentBalanceItems.length > 0) {
        return {
          s_oPYM: "Bank payment:optin",
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        };
      } else if (checkedCurrentBalanceItems.length == 0) {
        return {
          s_oPYM: "Bank payment:optout",
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        };
      }
    } else {
      return {
        s_oPYM: "Bank payment",
        s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
      };
    }
  } else if (!isBankpaymentSelscted) {
    if (showCurrentBalance) {
      if (checkedCurrentBalanceItems.length > 0) {
        return {
          s_oPYM: "Credit card:optin",
          s_oCCDT: {CreditCardType},
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        };
      } else if (checkedCurrentBalanceItems.length == 0) {
        return {
          s_oPYM: "Credit card:optout",
          s_oCCDT:  {CreditCardType},
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber
        };
      }
    } else {
      return {
        s_oPYM: "Credit card",
        s_oCCDT: {CreditCardType},
        s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber
      };
    }
  }

  return {
    s_oPYM: "",
    s_oCCDT: "",
  };
};


export const getOmnitureOnConfirmationFailure = (
  isBankpaymentSelscted: boolean,
  showCurrentBalance: boolean,
  checkedCurrentBalanceItems: PaymentItem[],
  CreditCardType?: string,
) => {

  if (isBankpaymentSelscted) {
    if (showCurrentBalance) {
      if (checkedCurrentBalanceItems.length > 0) {
        return {
          s_oPYM: "Bank payment:optin",
          s_OPID: ""
        };
      } else if (checkedCurrentBalanceItems.length == 0) {
        return {
          s_oPYM: "Bank payment:optout",
          s_OPID: ""
        };
      }
    } else {
      return {
        s_oPYM: "Bank payment",
        s_OPID: ""
      };
    }
  } else if (!isBankpaymentSelscted) {
    if (showCurrentBalance) {
      if (checkedCurrentBalanceItems.length > 0) {
        return {
          s_oPYM: "Credit card:optin",
          s_oCCDT: {CreditCardType},
          s_OPID: ""
        };
      } else if (checkedCurrentBalanceItems.length == 0) {
        return {
          s_oPYM: "Credit card:optout",
          s_oCCDT:  {CreditCardType},
          s_OPID: ""
        };
      }
    } else {
      return {
        s_oPYM: "Credit card",
        s_oCCDT: {CreditCardType},
        s_OPID: ""
      };
    }
  }

  return {
    s_oPYM: "",
    s_oCCDT: "",
    s_OPID:""
  };
}
