export const numberOnly = (e: any) => {
    e.target.value = e.target.value.replace(/[^0-9]/gi, "");
}

export const getMonthList = () => {
    return Array.from({ length: 12 }, (_, i) => {
        let month: string | number = i + 1;
        if (i < 9) {
        month = i + 1;
        month = "0" + month;
        }
        return month;
    });
}

export const getYearList = () => {
    let max = parseInt(new Date().getFullYear().toString().substring(2));
    let min = max + 9;
    let years = [];
    for (let i = max; i <= min; i++) {
        years.push(i);
    }
    return years;   
}