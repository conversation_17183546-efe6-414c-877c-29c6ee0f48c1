import * as React from "react";
import {Text} from "@bell/bell-ui-library"

export interface SingleRowInformationProps {
    label: string | React.ReactNode,
    value: React.ReactNode,
    className?: string,
    needSRText?: boolean,
    srText?: string,
    role?: string,
    isMultiBan?: boolean,
}

export const SingleRowInformation = ({ label, value, className, needSRText, srText, role, isMultiBan }: SingleRowInformationProps) => {
    return (
        <div className={[className,"payment-mb-5 last:payment-mb-0"].join(" ").trim()} role={role}>
            <Text elementType="div"
                className={isMultiBan
                    ? "brui-flex payment-justify-between sm:payment-justify-normal brui-text-14 brui-leading-18 brui-ml-10"
                    : "brui-flex payment-justify-between sm:payment-justify-normal brui-text-14 brui-leading-18"
                }
            >
                <label className={isMultiBan ? "sm:payment-w-[165px] payment-mr-30" : "sm:payment-w-[175px] payment-mr-30"}>
                    {isMultiBan
                        ? label
                        : <strong className="payment-leading-18">{label}</strong>
                    }
                </label>
                <Text elementType="div"
                    className="brui-text-gray brui-text-right sm:brui-text-left"
                    aria-hidden={needSRText}
                >
                    {value}
                </Text>
                {needSRText && (
                    <span className="brui-sr-only">
                        {srText}
                    </span>
                )}
            </Text>
        </div>
    );
}

export default SingleRowInformation;