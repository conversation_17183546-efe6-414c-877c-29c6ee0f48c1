import React, { forwardRef } from "react";
import { injectIntl } from "react-intl";
import { PaymentRadioCard } from "./PaymentMethodRadio";
import { FormControl, Text, InputText, Label, Select, SelectOption, ListItem, Divider, Price,Icon} from "@bell/bell-ui-library";
import { RadioCardBankDetails } from "./RadioCardBankDetails";
import { LightBoxFindYourTransaction } from "../LightBox";
import { PaymentInputFormFieldsPaymentAlreadyExist } from "../Form/PaymentInputFormFieldsPaymentAlreadyExist";
import { dropDownHeight, numberOnly, INTERACTBANKSERVERFAILURE } from "../../utils";
import { BankPaymentProps } from "../../models/BankPaymentPropsModel";
import { connect } from "react-redux";
import { State } from "../../store";
import { getRedirectUrl } from "../../store/Actions";
import {NotifCard} from "../NotifCard";
import { PaymentItem } from "../../models";

export const BankPaymentComponent = forwardRef<HTMLInputElement, BankPaymentProps>(function BankPaymentComponent(
  {
    intl,
    Checked,
    onChange,
    errorBankAccountHolderName,
    isInteracSelected,
    radioCardRef,
    handleBankRadioManualDetailsChange,
    isBankManualEnterDetails,
    isPreauth,
    hasBankAccountDetails,
    bankitems,
    handleBankRadioChange,
    bankListInterac,
    handleInteracSubmit,
    isBankChecked,
    inputRefs,
    errorBankName,
    errorBankTransit,
    errorBankAccountNumber,
    radioRef,
    bankList,
    redirectUrl,
    interacBankInfo,
    checkedBillItems,
    interactBankFailureInfo,
    creditCardAutopayOffers,
    debitCardAutopayOffers,
    language,
    isInteractEnabled,
    IsAutopayCreditEnabled
  },
  ref
) {
  const [interacDetails, setInteracDetails] = React.useState({
    bankAccountHolderName: interacBankInfo.accountHolderName,
    bankAccountNumber: interacBankInfo.bankAccountNumber,
    bankTransitCode: interacBankInfo.transitNumber,
    bankCode: interacBankInfo.bankCode
  });
  const BANK_PAYMENT_LABEL = intl.formatMessage({ id: "BANK_ACCOUNT_LABEL" })

  const handleBankAccountHolderNameChange = (e: any) => {
    const value = e.target.value;
    setInteracDetails({...interacDetails,
      bankAccountHolderName: value
    });
  }

  const handleBankTransitNumberChange = (e: any) => {
    const value = e.target.value;
    setInteracDetails({...interacDetails,
      bankTransitCode: value
    })
  }
  
  const checkedBanOffers = () => {
    var filteredOffer: any = [];
   debitCardAutopayOffers && debitCardAutopayOffers?.map((item) =>{
         checkedBillItems && checkedBillItems.map((billItem)=>{
          if(item.Ban == billItem.Ban){
            filteredOffer.push(item);
          }
         });
     });
   
    return filteredOffer;   
  }
    
  const debitOffers =      
  {
    label: intl.formatMessage({id:"PAYMENT_METHOD"}),            
    credits: bankitems && bankitems.length > 1 ? checkedBanOffers() : debitCardAutopayOffers
  };
 
  
  const handleBankAccountNumberChange = (e: any) => {
    const value = e.target.value;
    setInteracDetails({...interacDetails,
      bankAccountNumber: value
    })
  }

  const handleAnchorTagChange = () => {
    console.log("anchor tag is clicked");
    var arrayOfCheckedItems = new Array(checkedBillItems.length);
    checkedBillItems && checkedBillItems.map((item) => {
      if (!(arrayOfCheckedItems.includes(item.Ban)))
      {
        arrayOfCheckedItems.push(item.Ban)
      }
    });
    var checkedItems = arrayOfCheckedItems.filter(item => item !== null);
    window.sessionStorage.setItem("itemsChecked", JSON.stringify(checkedItems));
  }

  React.useEffect(() => {
    if(interacBankInfo != null && interacBankInfo.status === "SUCCESS"){
      setInteracDetails({
        bankAccountHolderName: interacBankInfo.accountHolderName,
        bankAccountNumber: interacBankInfo.bankAccountNumber,
        bankTransitCode: interacBankInfo.transitNumber,
        bankCode: interacBankInfo.bankCode
      })
    }
  }, [interacBankInfo])

  React.useEffect(() => {
    if(interactBankFailureInfo != null && interactBankFailureInfo.dataType == "error"){
      // set the RadiocardBankDetail to false for interac radio button and true for manual radio button
     if(interactBankFailureInfo.data.includes(INTERACTBANKSERVERFAILURE))
     {
      if(radioCardRef.manualDetails && radioCardRef.manualDetails.current) {
        radioCardRef.manualDetails.current.checked = true;
      }  
      if(radioCardRef.interac && radioCardRef.interac.current) {
        radioCardRef.interac.current.checked = false;
      }
    }
    }
  }, [interactBankFailureInfo])
  const getTotalOffers = () => {
     
    return bankitems && bankitems.length > 1 ? checkedBanOffers().reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
     
     const offersCount = ban.AutopayEligibleSubscribers.reduce(
       (count, subscriber) => count + subscriber.autopayOffers.length,
       0
     );
     return total + offersCount;
   }, 0): debitCardAutopayOffers && debitCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
     
     const offersCount = ban.AutopayEligibleSubscribers.reduce(
       (count, subscriber) => count + subscriber.autopayOffers.length,
       0
     );
     return total + offersCount;
   }, 0);
 };
 const getTotalCreditOffers = () => {
     
  return bankitems && bankitems.length > 1 ? checkedBanOffers().reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
   
   const offersCount = ban.AutopayEligibleSubscribers.reduce(
     (count, subscriber) => count + subscriber.autopayOffers.length,
     0
   );
   return total + offersCount;
 }, 0): creditCardAutopayOffers && creditCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
   
   const offersCount = ban.AutopayEligibleSubscribers.reduce(
     (count, subscriber) => count + subscriber.autopayOffers.length,
     0
   );
   return total + offersCount;
 }, 0);
};
  const [bankDetails, setBankDetails] = React.useState({
    bankAccountHolder: "",
    bankAccountNumber: "",
    bankAccountNumberMasked: "",
    bankTransit: "",
    bankCode: "",
  });
  const [existingBankPaymentDetails, setExistingBankPaymentDetails] = React.useState<PaymentItem[]>([]);
  const getExistingBankPaymentDetails = (details:any) => {
      setExistingBankPaymentDetails(details);
  }

  React.useEffect(() => {
    if (existingBankPaymentDetails.length > 0 && !isInteracSelected) {
      existingBankPaymentDetails.map((item) => {
        setBankDetails(prevDetails => ({
          ...prevDetails,
          bankAccountHolder: item.BankAccountDetails?.CardHolder || "",
          bankAccountNumber: item.BankAccountDetails?.AccountNumber || "",
          bankAccountNumberMasked: item.BankAccountDetails?.AccountNumberMasked || "",
          bankTransit: item.BankAccountDetails?.TransitCode || "",
          bankCode: item.BankAccountDetails?.BankCode || "",
        }))
      });
    } else if (existingBankPaymentDetails.length == 0 && !isInteracSelected) {
      setBankDetails(prevDetails => ({
        ...prevDetails,
        bankAccountHolder: "",
        bankAccountNumber: "",
        bankAccountNumberMasked: "",
        bankTransit: "",
        bankCode: "",
      }))
      bankDetails
    }
  }, [existingBankPaymentDetails]);

  const handleManualBankAccountHolderOnChange = (e:any) => {
    const value = e.target.value;
    setBankDetails({
      ...bankDetails,
      bankAccountHolder: value
    })
  }

  const handleManualBankTransitCodeOnChange = (e:any) => {
    const value = e.target.value;
    setBankDetails({
      ...bankDetails,
      bankTransit: value
    })
  }

  const handleManualBankAccountOnChange = (e:any) => {
    const value = e.target.value;
    setBankDetails({
      ...bankDetails,
      bankAccountNumber: value
    })
  }
  const isTrueAutopay = () => {
  return debitOffers?.credits?.some((debit: any) =>
    debit.AutopayEligibleSubscribers?.some((item: any) =>
      item?.autopayOffers?.some(
        (credit: any) =>
          credit.eligiblePaymentMethods?.includes("C") &&
          credit.eligiblePaymentMethods?.includes("D")
      )
    )
  ) || false;
}

  const handleManualBankAccountMaskedOnChange = (e:any) => {
    const value = e.target.value;
    setBankDetails({
      ...bankDetails,
      bankAccountNumberMasked: value,
      bankAccountNumber: value,
    })
  }
  //get InteracBankInfo if Empty, to make sure that the manual bank form only shows when manual bank radio = Check and interactBankin has a value .
  const isInteracBankInfoEmpty = Object.values(interacBankInfo).every(value => value === "");

  const CTA_INTERAC = intl.formatMessage({ id: "CTA_INTERAC" });
  const CTA_INTERAC_SR = intl.formatMessage({ id: "CTA_INTERAC_SR" });
  const InteractEnable = isInteractEnabled == "ON" ? true : false

  return (
    <div className="brui-mb-15">
      <PaymentRadioCard
        id="payment-radio-bank"
        name="payment-radio"
        label={BANK_PAYMENT_LABEL}
        headingLevel="h3"
        defaultChecked={Checked ? true : undefined}
        ref={ref}
        onChange={onChange}
      >
         {IsAutopayCreditEnabled && <div>      

          <NotifCard
          hasNotifCard={getTotalOffers() > 0}
          variant="greatNews"
          label={intl.formatMessage({ id: "GREAT_NEWS" })}
          label1={
            isTrueAutopay()
              ? getTotalOffers() > 1
                ? intl.formatMessage({ id: "LABEL_LOADED_OFFERS_TITLE_TRUEAUTOPAY" })
                : intl.formatMessage({ id: "LABEL_LOADED_OFFER_TITLE_TRUEAUTOPAY" })
              : getTotalOffers() > 1
              ? intl.formatMessage({ id: "LABEL_LOADED_OFFERS_DEBIT_TITLE" })
              : intl.formatMessage({ id: "LABEL_LOADED_OFFER_DEBIT_TITLE" })
          }
          label2={intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING" })}
          label3={
            getTotalOffers() > 1
              ? intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE" })
              : intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE" })
          }
        >
          {debitOffers?.credits?.map((debit: any, debitIdx: number) => (
            <React.Fragment key={debit.Ban || debitIdx}>
              {bankitems && bankitems.length > 1 && (
                <p className="payment-text-14 payment-text-gray">
                  {debit.banInfo && debit.banInfo.nickName}:
                </p>
              )}
              {debit.AutopayEligibleSubscribers?.map((item: any, itemIdx: number) =>
                item?.autopayOffers?.map((credit: any, creditIdx: number) => (
                  <ul
                    className="payment-list-disc payment-list-inside payment-mb-10"
                    key={`${item.subscriberTelephoneNumber}-${creditIdx}`}
                  >
                    <ListItem className="payment-text-14 payment-text-gray payment-leading-18">
                      {item.subscriberTelephoneNumber} -&nbsp;
                      <Price
                        className="payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal"
                        showZeroDecimalPart={false}
                        price={credit.discountAmount}
                        variant="defaultPrice"
                        suffixText="perMonth"
                        language={language}
                      />
                    </ListItem>
                  </ul>
                ))
              )}
            </React.Fragment>
          ))}
        </NotifCard>          
              {/* {getTotalOffers() == 0 ?
                    <Text className="brui-text-14 brui-text-gray">
                    {intl.formatMessage({ id: "SORRY_MESSAGE" })} 
                  </Text> : " "
                    } */}
                     {debitCardAutopayOffers && debitCardAutopayOffers.length > 0 &&  getTotalOffers() == 0 && getTotalCreditOffers() > 0 ?
                       <Text role="alert" className="payment-bg-gray-3 payment-flex payment-flex-col sm:payment-flex-row payment-p-15 payment-gap-15 payment-rounded-[16px] payment-mb-10" elementType="div">
      <div className="payment-flex payment-size-20 payment-items-center payment-justify-center">
          <Icon  className="payment-text-20 payment-text-blue" iconClass="bi_brui" iconName="bi_info_notif_small"></Icon>
      </div>
      <Text  id ={getTotalOffers() == 0 ? "discount-offer":""}className="payment-text-14 payment-leading-20 payment-text-gray" elementType="p"> {intl.formatMessage({ id: "SORRY_MESSAGE" })}</Text>
  </Text> : ""}
                    
                </div>}
         
        <div>
          <div role="radiogroup" aria-labelledby="payment-radio-bank">
            <form noValidate>
              <FormControl>
                {!isInteracSelected && (
                  <div>
                    {/* INTERAC */}
                    {InteractEnable &&
                    <div>
                      <RadioCardBankDetails
                        id="radio-1"
                        name="bank-details-radio"
                        label={intl.formatMessage({ id: "BANK_ACCOUNT_AUTOMATIC_LABEL" })}
                        describe={intl.formatMessage({ id: "BANK_ACCOUNT_AUTOMATIC_DESCRIPTION" })}
                        isInterac
                        ref={radioCardRef.interac}
                        defaultChecked={true}
                        interactIconPath={intl.formatMessage({ id: "INTERAC_BOX_LOGO" })}
                        onChange={handleBankRadioManualDetailsChange}
                      >
                        <div>
                          <ul className="max-318:payment-w-auto max-318:payment-h-auto payment-gap-x-15 sm:payment-gap-x-45 payment-w-1/2 sm:payment-w-[250px] payment-flex payment-flex-wrap payment-flex-col payment-h-[90px] payment-list-disc payment-list-inside payment-mt-15 payment-ml-10">
                            {/* map data based on mockup (for visual purpose only) with multiple information per section */}
                            {bankListInterac.map((item) => (
                              <ListItem className="brui-text-gray brui-leading-18">
                               <Text className="payment-mt-10 brui-text-14"> {item}</Text>
                               {/* <Text className="payment-mt-10 brui-text-14"> {intl.formatMessage({ id: item.bankname })}</Text>   */}
                              </ListItem>
                            ))}
                          </ul>
                          <div className="payment-mt-15">
                            <a href={redirectUrl.externalRedirectUrl} onClick={handleAnchorTagChange} className="brui-text-15 brui-leading-17 brui-py-7 brui-px-30 brui-inline-block brui-rounded-30 
                            brui-bg-blue-1 brui-text-white brui-border-blue-1 brui-border-2 enabled:hover:brui-bg-blue enabled:hover:brui-border-blue focus:brui-outline-blue-2 
                            focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-bg-gray-2 
                            disabled:brui-text-white disabled:brui-border-gray-2" role="button">
                              <span aria-hidden="true" dangerouslySetInnerHTML={{ __html: CTA_INTERAC }}></span>
                              <span className="payment-sr-only" dangerouslySetInnerHTML={{ __html: CTA_INTERAC_SR }}></span>
                            </a>
                            {/* <Button variant="primary" onClick={handleInteracSubmit}>{intl.formatMessage({ id: "CTA_INTERAC" })}</Button> */}
                          </div>
                        </div>
                      </RadioCardBankDetails>
                    </div>
                    }
                    {/* MANUAL ENTER DETAILS */}
                    <div className="payment-mt-15">
                      <RadioCardBankDetails
                        id="radio-2"
                        name="bank-details-radio"
                        label={existingBankPaymentDetails.length > 0 ? intl.formatMessage({ id: "EXISTING_BANK_ACCOUNT_MANUAL_DETAILS_LABEL" }) : intl.formatMessage({ id: "BANK_ACCOUNT_MANUAL_DETAILS_LABEL" })}
                        describe={intl.formatMessage({ id: "BANK_ACCOUNT_MANUAL_DETAILS_DESCRIPTION" })}
                        ref={radioCardRef.manualDetails}
                        onChange={handleBankRadioManualDetailsChange}
                        defaultChecked={InteractEnable ? undefined : true}
                      >                         
                        <Divider direction="horizontal" width={1} className="payment-my-30 payment-bg-gray-4" />
                        <div className="brui-flex brui-flex-row payment-gap-4 brui-items-center payment-mb-30 md:payment-mb-45">
                          <div className="brui-text-14 brui-text-gray brui-leading-18">{intl.formatMessage({ id: "BANK_NEED_HELP" })}</div>
                          <LightBoxFindYourTransaction />
                        </div>
                        <div>
                          {(isBankManualEnterDetails || !isInteracBankInfoEmpty || !InteractEnable || interactBankFailureInfo?.dataType == "error") && (
                            <form noValidate>
                              {(isPreauth && hasBankAccountDetails) && (
                                <FormControl
                                  className="sm:brui-flex-row payment-mt-30">
                                  <PaymentInputFormFieldsPaymentAlreadyExist
                                    isCreditCardPayment={false}
                                    isBankPayment={true}
                                    isPreAuth={isPreauth}
                                    paymentItems={bankitems}
                                    ref={radioRef}
                                    onChange={handleBankRadioChange}
                                    getExistingBankPaymentDetails={getExistingBankPaymentDetails}
                                  />
                                </FormControl>
                              )}

                              {(isBankChecked || (!isPreauth && !isBankChecked) || (isPreauth) || interacBankInfo) && (
                                <>
                                  <FormControl className="sm:brui-flex-row payment-mt-30">
                                    <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                                      <Label id="bank-name-label" htmlFor="bank-name" isError={errorBankName} required={true}  className={errorBankName ? "payment-error-required" : ""}>
                                        {intl.formatMessage({ id: "BANK_NAME_LABEL" })}
                                      </Label>
                                    </div>
                                    
                                    { bankDetails.bankCode != "" && (
                                      <Select
                                        className="sm:!payment-w-[280px] brui-text-gray"
                                        id="bank-name"
                                        name="select-bank-name"
                                        hasError={errorBankName}
                                        errorMessage={intl.formatMessage({ id: "BANK_NAME_ERROR_LABEL" })}
                                        aria-labelledby="bank-name-label"
                                        aria-describedby=" "
                                        ref={inputRefs.inputBankName}
                                        defaultValue={bankDetails.bankCode}
                                        dropDownHeight={dropDownHeight}
                                        placeHolder={intl.formatMessage({ id: "SELECT_BANK_PLACEHOLDER" })}
                                      >
                                        {bankList.map((item) => (
                                          item.Text != "" && (
                                            <SelectOption value={item.Value}
                                              id={`option-bank-${item.Value}`}
                                              displayName={item.Text} />
                                          )
                                        ))}
                                      </Select>
                                    )}

                                    { bankDetails.bankCode == "" && (
                                      <Select
                                        className="sm:!payment-w-[280px] brui-text-gray"
                                        id="bank-name"
                                        name="select-bank-name"
                                        hasError={errorBankName}
                                        errorMessage={intl.formatMessage({ id: "BANK_NAME_ERROR_LABEL" })}
                                        aria-labelledby="bank-name-label"
                                        aria-describedby=" "
                                        ref={inputRefs.inputBankName}
                                        dropDownHeight={dropDownHeight}
                                        placeHolder={intl.formatMessage({ id: "SELECT_BANK_PLACEHOLDER" })}
                                      >
                                        {bankList.map((item) => (
                                          item.Text != "" && (
                                            <SelectOption value={item.Value}
                                              id={`option-bank-${item.Value}`}
                                              displayName={item.Text} />
                                          )
                                        ))}
                                      </Select>
                                    )}
                                  </FormControl>

                                  <FormControl className="sm:brui-flex-row payment-mt-30">
                                    <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                                      <Label htmlFor="bank-holder-name" isError={errorBankAccountHolderName} required={true} className={errorBankAccountHolderName ? "payment-error-required" : ""}>
                                        {intl.formatMessage({ id: "BANK_HOLDER_NAME_LABEL" })}
                                      </Label>
                                    </div>
                                    <InputText
                                      className="sm:!payment-w-[280px] !brui-text-gray"
                                      id="bank-holder-name"
                                      required={true}
                                      isError={errorBankAccountHolderName}
                                      errorMessage={intl.formatMessage({ id: "BANK_HOLDER_NAME_ERROR_LABEL" })}
                                      minLength={5}
                                      maxLength={70}
                                      ref={inputRefs.inputBankAccountHolder}
                                      value={bankDetails.bankAccountHolder}
                                      onChange={handleManualBankAccountHolderOnChange}
                                    />
                                  </FormControl>

                                  <FormControl className="sm:brui-flex-row payment-mt-30">
                                    <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                                      <Label htmlFor="bank-transit-number" isError={errorBankTransit} required={true}  className={errorBankTransit ? "payment-error-required" : ""}>
                                        {intl.formatMessage({ id: "BANK_TRANSIT_NUMBER_LABEL" })}
                                      </Label>
                                      <Text elementType="div" className="brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray">
                                        {intl.formatMessage({ id: "BANK_TRANSIT_NUMBER_DESCRIPTION" })}
                                      </Text>
                                    </div>
                                    <InputText
                                      className="!payment-w-[140px]"
                                      id="bank-transit-number"
                                      required={true}
                                      isError={errorBankTransit}
                                      errorMessage={intl.formatMessage({ id: "BANK_TRANSIT_ERROR_LABEL" })}
                                      minLength={5}
                                      maxLength={5}
                                      onInput={(e: any) => numberOnly(e)}
                                      ref={inputRefs.inputTransitNumber}
                                      value={bankDetails.bankTransit}
                                      onChange={handleManualBankTransitCodeOnChange}
                                    />
                                  </FormControl>

                                  <FormControl className="sm:brui-flex-row payment-mt-30">
                                    <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                                      <Label htmlFor="bank-account-number" isError={errorBankAccountNumber} required={true}  className={errorBankAccountNumber ? "payment-error-required" : ""}>
                                        {intl.formatMessage({ id: "BANK_ACCOUNT_NUMBER_LABEL" })}
                                      </Label>
                                      <Text elementType="div" className="brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray">
                                        {intl.formatMessage({ id: "BANK_ACCOUNT_NUMBER_DESCRIPTION" })}
                                      </Text>
                                    </div>
                                    <div>
                                      {existingBankPaymentDetails.length > 0 && (
                                        <InputText
                                          className="!payment-w-[140px]"
                                          id="bank-account-number"
                                          required={true}
                                          isError={errorBankAccountNumber}
                                          minLength={5}
                                          maxLength={12}
                                          errorMessage={intl.formatMessage({ id: "BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL" })}
                                          onInput={(e: any) => numberOnly(e)}
                                          value={bankDetails.bankAccountNumberMasked}
                                          onChange={handleManualBankAccountMaskedOnChange}
                                        />
                                      )}
                                      <InputText
                                        className="!payment-w-[140px]"
                                        id="bank-account-number"
                                        required={true}
                                        isError={existingBankPaymentDetails.length > 0 ? false : errorBankAccountNumber}
                                        minLength={5}
                                        maxLength={12}
                                        errorMessage={intl.formatMessage({ id: "BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL" })}
                                        onInput={(e: any) => numberOnly(e)}
                                        ref={inputRefs.inputBankAccountNumber}
                                        value={bankDetails.bankAccountNumber}
                                        onChange={handleManualBankAccountOnChange}
                                        type={existingBankPaymentDetails.length > 0 ? "hidden" : "text"}
                                      />
                                    </div>
                                  </FormControl>

                                  <Text className="brui-text-14 brui-leading-18 brui-text-gray payment-mt-30 sm:payment-mt-45 brui-inline-block">{intl.formatMessage({ id: "REQUIRED_LABEL" })}</Text>
                                </>
                              )}
                            </form>
                          )}
                        </div>
                      </RadioCardBankDetails>
                    </div>
                  </div>
                )}
              </FormControl>
            </form>
          </div>

          {/* BANK DETAILS FORM - WHEN INTERAC IS SELECTED */}
          {isInteracSelected && InteractEnable && (
            <div>
              <form noValidate>
                <FormControl className="sm:brui-flex-row payment-mt-30">
                  <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                    <Label id="bank-name-label" htmlFor="bank-name" isError={false} required={true}>
                      {intl.formatMessage({ id: "BANK_NAME_LABEL" })}
                    </Label>
                  </div>
                  <Select
                    className="sm:!payment-w-[280px] brui-text-gray"
                    id="bank-name"
                    name="select-bank-name"
                    hasError={false}
                    errorMessage={"Please select at least one option"}
                    aria-labelledby="bank-name-label"
                    ref={inputRefs.inputBankName}
                    defaultValue={interacDetails.bankCode}
                    dropDownHeight={dropDownHeight}
                  >
                    {bankList.map((item) => (
                      item.Text != "" && (
                        <SelectOption value={item.Value}
                          id={`option-bank-${item.Value}`}
                          displayName={item.Text} />
                      )
                    ))}
                  </Select>
                </FormControl>

                <FormControl className="sm:brui-flex-row payment-mt-30">
                  <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                    <Label htmlFor="bank-holder-name" isError={false} required={true}>
                      {intl.formatMessage({ id: "BANK_HOLDER_NAME_LABEL" })}
                    </Label>
                  </div>
                  <InputText
                    className="sm:!payment-w-[280px] !brui-text-gray"
                    id="bank-holder-name"
                    required={true}
                    isError={errorBankAccountHolderName}
                    errorMessage={intl.formatMessage({ id: "BANK_HOLDER_NAME_ERROR_LABEL" })}
                    minLength={5}
                    maxLength={70}
                    ref={inputRefs.inputBankAccountHolder}
                    value={interacDetails.bankAccountHolderName}
                    onChange={handleBankAccountHolderNameChange}
                  />
                </FormControl>

                <FormControl className="sm:brui-flex-row payment-mt-30">
                  <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                    <Label htmlFor="bank-transit-number" isError={false} required={true}>
                      {intl.formatMessage({ id: "BANK_TRANSIT_NUMBER_LABEL" })}
                    </Label>
                    <Text elementType="div" className="brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray">
                      {intl.formatMessage({ id: "BANK_TRANSIT_NUMBER_DESCRIPTION" })}
                    </Text>
                  </div>
                  <InputText
                    className="!payment-w-[140px]"
                    id="bank-transit-number"
                    required={true}
                    isError={false}
                    errorMessage={"This is required field"}
                    minLength={7}
                    maxLength={12}
                    onInput={(e: any) => numberOnly(e)}
                    ref={inputRefs.inputTransitNumber}
                    value={interacDetails.bankTransitCode}
                    onChange={handleBankTransitNumberChange}
                  />
                </FormControl>

                <FormControl className="sm:brui-flex-row payment-mt-30">
                  <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                    <Label htmlFor="bank-account-number" isError={false} required={true}>
                      {intl.formatMessage({ id: "BANK_ACCOUNT_NUMBER_LABEL" })}
                    </Label>
                    <Text elementType="div" className="brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray">
                      {intl.formatMessage({ id: "BANK_ACCOUNT_NUMBER_DESCRIPTION" })}
                    </Text>
                  </div>
                  <div>
                    <InputText
                      className="!payment-w-[140px]"
                      id="bank-account-number"
                      required={true}
                      isError={false}
                      errorMessage={"This is required field"}
                      onInput={(e: any) => numberOnly(e)}
                      ref={inputRefs.inputBankAccountNumber}
                      value={interacDetails.bankAccountNumber}
                      onChange={handleBankAccountNumberChange}
                    />
                    {/* <AccountFetched hasAccountFetched={hasAccountFetched} /> */}
                    <div style={{marginTop: "5px"}} className="brui-flex brui-gap-10 brui-mt-5">
                      <span style={{marginRight: "8px"}} className="bi_small_checkmark_full bi_brui brui-text-green" role="img" aria-hidden="true" aria-label=" "></span>
                      <div id="account-fetched" className="brui-flex brui-flex-col brui-text-12 brui-leading-14">
                        <span id="account-fetched-message" className="brui-text-black brui-font-bold">{intl.formatMessage({ id: "INTERAC_FETCHED_LABEL" })}</span>
                        <span className="brui-text-gray">{intl.formatMessage({ id: "INTERAC_FETCHED_SUBTITLE" })}</span>
                      </div>
                    </div>
                  </div>
                </FormControl>

                <Text className="brui-text-14 brui-leading-18 brui-text-gray payment-mt-30 sm:payment-mt-45 brui-inline-block">{intl.formatMessage({ id: "REQUIRED_LABEL" })}</Text>
              </form>
            </div>
          )}
        </div>
      </PaymentRadioCard>
    </div>
  );
});

const mapDispatchToProps = (dispatch: React.Dispatch<any>) => {
  return {
      redirectUrlAction: () => {dispatch(getRedirectUrl({}))},
  }
}

const mapStateToProps = (state: State) => {
  return {
      redirectUrl: state.redirectUrl,
      interacBankInfo: state.interacBankInfo,
      interactBankFailureInfo: state.interactBankFailureInfo
  }
}

export const BankPayment = connect(mapStateToProps, mapDispatchToProps)(injectIntl(BankPaymentComponent));
