import * as React from "react";
import {Alert, Heading, Text} from "@bell/bell-ui-library";
import { injectIntl } from "react-intl";
import {INTERACTBANKSERVERFAILURE} from "../../utils"


interface ErrorInteracProps {
    intl: any;
    interact : any;
}

const AlertCreditCardErrorInteracComponent = ({intl, interact}: ErrorInteracProps) => {

  return (
    <Alert 
    variant="error" 
    className="payment-block sm:payment-flex brui-px-0 sm:payment-px-30 payment-py-30 brui-border-x-0 sm:payment-border-x-1 payment-border-y-1 payment-relative brui-rounded-none sm:payment-rounded-20" 
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-0 md:payment-pt-7">
        <Heading level="h2" variant="xs" className=" sm:brui-mt-7 brui-mb-15 md:payment-mb-7 brui-font-sans brui-leading-22">
          <span aria-hidden="true">{interact.includes(INTERACTBANKSERVERFAILURE) ?intl.formatMessage({id:"ALERT_ERROR_HEADING_INTERAC"}) : intl.formatMessage({id:"ALERT_ERROR_HEADING_INTERAC"}) }</span>
          <span className="payment-sr-only">{ interact.includes(INTERACTBANKSERVERFAILURE) ? intl.formatMessage({id:"ALERT_ERROR_HEADING_INTERAC_SR"}) : intl.formatMessage({id:"ALERT_ERROR_HEADING_INTERAC_SR"})}</span>
        </Heading>
          <div>
            <p className="brui-text-14 brui-my-15 brui-text-gray">{interact.includes(INTERACTBANKSERVERFAILURE) ? intl.formatMessage({id:"ALERT_ERROR_HEADING_INTERAC_DESC"}) : intl.formatMessage({id:"ALERT_ERROR_HEADING_INTERAC_DESC"})}</p>
        </div>
    </Text>
    </Alert>
    );
}

export const AlertCreditCardErrorInterac = injectIntl(AlertCreditCardErrorInteracComponent)
